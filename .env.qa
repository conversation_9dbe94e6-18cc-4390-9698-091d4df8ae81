ENV=qa
PORT=3000
MSK_BROKERS='["b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198","b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198","b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198"]'

MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev"
DATABASE_NAME=coddnQA

##############SPECTRUM DATABASE CONNECTION
SPECTRUM_MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev"
SPECTRUM_DATABASE_NAME=spectrum_events_db_qa

AWS_ACCESS_KEY_ID=********************
AWS_SERCRET_KEY_ID=XB5g7U5NBtDCzd5vTXoJLumcDEuzA8sOulVO2tIg
AWS_REGION=us-east-2

MSK_PYTHON_BROKERS='b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198'

AUTH0_BASE_URL=http://localhost:3000
# AUTH0_ISSUER_URL=https://dev-mquz0q4oastb8zws.us.auth0.com
AUTH0_ISSUER_URL=https://auth-demo.aerodefense.tech
AUTH0_AUDIENCE=https://api-qa.aerodefense.tech
AUTH0_CLIENT_ID=P8bDXvNuR432Wayf0ceKnXYVOXX82KHh
AUTH0_SECRET_KEY=****************************************************************

AUTH0_MANAGEMENT_API=https://dev-mquz0q4oastb8zws.us.auth0.com
AUTH0_MANAGEMENT_API_CLIENT_ID=QMGaKc2LSBZgvVB9Pdztbwj8B5X4sGL9
AUTH0_MANAGEMENT_API_CLIENT_SECRET=****************************************************************

REDIS_HOST="coddn-api-redis-9nthzl.serverless.use2.cache.amazonaws.com"
GROUP_ID=qaGroupApi

RID_UI_TOPIC="DETECTION_QA"
RID_TOPIC="RID_QA"

APP_SYNC_URL='https://3ehud5ztijb4rdwy3l3f4iblba.appsync-api.us-east-2.amazonaws.com/graphql'
APP_SYNC_API_KEY='da2-ylyaaxohxre2bfthzifptrsnw4'

REDIS_HOST="rediss://valkey-connect:<EMAIL>:6379"
ENABLE_CACHE=false

##############SPECTRUM DATABASE CONNECTION
SPECTRUM_MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev"
SPECTRUM_DATABASE_NAME=

##############FEATURE_FLAGS
SPECTRUM_FLAG=true
