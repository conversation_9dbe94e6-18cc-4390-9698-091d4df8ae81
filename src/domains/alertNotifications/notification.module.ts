import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { NotificationService } from './notification.service';
import { NotificationsController } from './notification.controller';
import NotificationSchema from './notification.schema';
import { OrganizationModule } from '../organizations/organization.module';
import Constants from 'src/common/constants';
import { SpectrumEventService } from '../spectrumEvents/spectrumEvent.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Constants.notification, schema: NotificationSchema }]),
    OrganizationModule,
  ],
  controllers: [NotificationsController],
  providers: [NotificationService, SpectrumEventService],
  exports: [NotificationService],
})
export class NotificationModule {}
