import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Constants from 'src/common/constants';
import { SpectrumEvent } from './spectrumEvent.interface';

@Injectable()
export class SpectrumEventModel {
  constructor(
    @InjectModel(Constants.spectrum_profile_events, 'spectrum')
    private readonly spectrumEventModel: Model<SpectrumEvent>,
  ) {}

  get model(): Model<SpectrumEvent> {
    return this.spectrumEventModel;
  }

  async create(spectrumEventData: Partial<SpectrumEvent>): Promise<SpectrumEvent> {
    const createdEvent = new this.spectrumEventModel(spectrumEventData);
    return await createdEvent.save();
  }

  async findById(id: string): Promise<SpectrumEvent | null> {
    return await this.spectrumEventModel.findById(id).exec();
  }

  async findOne(query: any): Promise<SpectrumEvent | null> {
    return await this.spectrumEventModel.findOne(query).exec();
  }

  async find(query: any, options?: any): Promise<SpectrumEvent[]> {
    let queryBuilder = this.spectrumEventModel.find(query);
    
    if (options?.sort) {
      queryBuilder = queryBuilder.sort(options.sort);
    }
    
    if (options?.limit) {
      queryBuilder = queryBuilder.limit(options.limit);
    }
    
    if (options?.skip) {
      queryBuilder = queryBuilder.skip(options.skip);
    }
    
    return await queryBuilder.exec();
  }

  async findByIdAndUpdate(id: string, updateData: Partial<SpectrumEvent>, options?: any): Promise<SpectrumEvent | null> {
    return await this.spectrumEventModel.findByIdAndUpdate(id, updateData, options).exec();
  }

  async updateOne(query: any, updateData: Partial<SpectrumEvent>): Promise<any> {
    return await this.spectrumEventModel.updateOne(query, updateData).exec();
  }

  async updateMany(query: any, updateData: Partial<SpectrumEvent>): Promise<any> {
    return await this.spectrumEventModel.updateMany(query, updateData).exec();
  }

  async deleteOne(query: any): Promise<any> {
    return await this.spectrumEventModel.deleteOne(query).exec();
  }

  async deleteMany(query: any): Promise<any> {
    return await this.spectrumEventModel.deleteMany(query).exec();
  }

  async countDocuments(query: any): Promise<number> {
    return await this.spectrumEventModel.countDocuments(query).exec();
  }

  async aggregate(pipeline: any[]): Promise<any[]> {
    return await this.spectrumEventModel.aggregate(pipeline).exec();
  }
}
