import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Constants from 'src/common/constants';
import { SpectrumEvent } from './spectrumEvent.interface';
import { OrganizationService } from 'src/domains/organizations/organization.service';

@Injectable()
export class SpectrumEventService {
  constructor(
    @InjectModel(Constants.spectrum_profile_events, 'spectrum')
    private readonly spectrumEventModel: Model<SpectrumEvent>,
    private readonly organizationService: OrganizationService
  ) {}

  async createSpectrumEvent(spectrumEventData: Partial<SpectrumEvent>): Promise<SpectrumEvent> {
    try {
      const createdEvent = new this.spectrumEventModel(spectrumEventData);
      return await createdEvent.save();
    } catch (error) {
      console.error('Error creating spectrum event:', error);
      throw error;
    }
  }

  async findSpectrumEventById(id: string): Promise<SpectrumEvent | null> {
    try {
      return await this.spectrumEventModel.findById(id).exec();
    } catch (error) {
      console.error('Error finding spectrum event by ID:', error);
      throw error;
    }
  }

  async findIncompleteSpectrumEvent(orgId: string) {
    try {
      const auth0 = await this.organizationService.findByAuth0Id(orgId);
      return await this.spectrumEventModel.aggregate([
        {
          $match: {
            ORG_ID: auth0._id.toString(),
            COMPLETE: 0
          }
        },
        {
          $lookup: {
            from: Constants.spectrum_history_events,
            localField: 'EVENT_ID',
            foreignField: 'EVENT_ID',
            as: 'PROCESSED_ITEMS'
          }
        }
      ]);
    } catch (error) {
      console.error('Error finding spectrum event by event ID:', error);
      throw error;
    }
  }

  async findSpectrumEventByEventId(eventId: string, orgId: string) {
    try {
      const auth0 = await this.organizationService.findByAuth0Id(orgId);
      return await this.spectrumEventModel.aggregate([
        {
          $match: {
            ORG_ID: auth0._id.toString(),
            EVENT_ID: eventId
          }
        },
        {
          $lookup: {
            from: Constants.spectrum_history_events,
            localField: 'EVENT_ID',
            foreignField: 'EVENT_ID',
            as: 'PROCESSED_ITEMS'
          }
        }
      ]);
    } catch (error) {
      console.error('Error finding spectrum event by event ID:', error);
      throw error;
    }
  }

  async findSpectrumEventsByOrgId(orgId: string, limit: number = 100, skip: number = 0): Promise<SpectrumEvent[]> {
    try {
      return await this.spectrumEventModel
        .find({ org_id: orgId, isDeleted: false })
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip(skip)
        .exec();
    } catch (error) {
      console.error('Error finding spectrum events by org ID:', error);
      throw error;
    }
  }

  async findSpectrumEventsByType(eventType: string, limit: number = 100, skip: number = 0): Promise<SpectrumEvent[]> {
    try {
      return await this.spectrumEventModel
        .find({ event_type: eventType, isDeleted: false })
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip(skip)
        .exec();
    } catch (error) {
      console.error('Error finding spectrum events by type:', error);
      throw error;
    }
  }

  async findSpectrumEventsByDeviceId(deviceId: string, limit: number = 100, skip: number = 0): Promise<SpectrumEvent[]> {
    try {
      return await this.spectrumEventModel
        .find({ 'device_info.device_id': deviceId, isDeleted: false })
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip(skip)
        .exec();
    } catch (error) {
      console.error('Error finding spectrum events by device ID:', error);
      throw error;
    }
  }

  async findSpectrumEventsByDateRange(
    startDate: Date, 
    endDate: Date, 
    orgId?: string, 
    limit: number = 100, 
    skip: number = 0
  ): Promise<SpectrumEvent[]> {
    try {
      const query: any = {
        timestamp: { $gte: startDate, $lte: endDate },
        isDeleted: false
      };

      if (orgId) {
        query.org_id = orgId;
      }

      return await this.spectrumEventModel
        .find(query)
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip(skip)
        .exec();
    } catch (error) {
      console.error('Error finding spectrum events by date range:', error);
      throw error;
    }
  }

  async updateSpectrumEvent(id: string, updateData: Partial<SpectrumEvent>): Promise<SpectrumEvent | null> {
    try {
      return await this.spectrumEventModel
        .findByIdAndUpdate(id, { ...updateData, updatedAt: new Date() }, { new: true })
        .exec();
    } catch (error) {
      console.error('Error updating spectrum event:', error);
      throw error;
    }
  }

  async deleteSpectrumEvent(id: string, deletedBy?: string): Promise<SpectrumEvent | null> {
    try {
      return await this.spectrumEventModel
        .findByIdAndUpdate(
          id, 
          { 
            isDeleted: true, 
            deletedAt: new Date(),
            deletedBy: deletedBy,
            updatedAt: new Date()
          }, 
          { new: true }
        )
        .exec();
    } catch (error) {
      console.error('Error deleting spectrum event:', error);
      throw error;
    }
  }

  async getSpectrumEventCount(orgId?: string): Promise<number> {
    try {
      const query: any = { isDeleted: false };
      if (orgId) {
        query.org_id = orgId;
      }
      return await this.spectrumEventModel.countDocuments(query).exec();
    } catch (error) {
      console.error('Error getting spectrum event count:', error);
      throw error;
    }
  }
}
